# 🎯 按键触发平均值更新功能说明

## 📋 功能概述

实现了一个智能的数据显示系统：
- **实时数据采集**：摄像头持续发送数据，STM32计算移动平均值
- **按键触发更新**：只有按下HMI界面的"开始"按键时才更新显示屏
- **数据保持**：按键后显示保持不变，直到下次按键

## 🔧 工作原理

### 1. 数据采集阶段
```
摄像头发送数据 → STM32接收 → 存入缓冲区 → 计算平均值 → 等待按键
```

### 2. 按键触发阶段
```
按下"开始"按键 → 发送@...内容...\n → STM32接收 → 更新显示屏 → 保持显示
```

## 📊 缓冲区设计

### 距离数据缓冲区
- **大小**：20个数据点
- **类型**：浮点数组 `distance_buffer[20]`
- **用途**：存储最近20次距离测量值

### 图形尺寸缓冲区
- **大小**：20个数据点  
- **类型**：浮点数组 `shape_buffer[20]`
- **用途**：存储最近20次图形尺寸测量值

## 🎮 HMI按键设置

### 按键配置
1. **控件类型**：按钮 (Button)
2. **按键文本**：开始
3. **发送格式**：`@开始\n`
4. **触发方式**：按下时发送

### 按键事件处理
```c
// 检查是否是开始按键命令（@...内容...\n格式）
if(hmi_data[0] == '@' && strstr(hmi_data, "\n") != NULL) {
    Serial_SendString("Start button pressed!\r\n");
    handle_start_button();
}
```

## 📈 平均值计算

### 距离平均值
```c
float get_distance_average(void) {
    if (distance_buffer_count == 0) return 0.0f;
    
    float sum = 0.0f;
    for (uint8_t i = 0; i < distance_buffer_count; i++) {
        sum += distance_buffer[i];
    }
    return sum / distance_buffer_count;
}
```

### 图形尺寸平均值
```c
float get_shape_average(void) {
    if (shape_buffer_count == 0) return 0.0f;
    
    float sum = 0.0f;
    for (uint8_t i = 0; i < shape_buffer_count; i++) {
        sum += shape_buffer[i];
    }
    return sum / shape_buffer_count;
}
```

## 🔄 数据流程

### 摄像头数据接收
1. **接收格式**：`@DISTANCE:12.34` 或 `@CIRCLE:5.67`
2. **数据解析**：提取数值并分类
3. **缓冲区存储**：添加到对应缓冲区
4. **实时反馈**：串口输出当前平均值

### 按键触发更新
1. **按键检测**：检测HMI发送的`@开始\n`命令
2. **平均值计算**：计算当前缓冲区平均值
3. **显示更新**：发送到HMI对应控件
4. **状态保持**：显示值保持不变直到下次按键

## 📺 显示映射

### 控件对应关系
- **距离D** → `index.x2.val` (显示距离平均值)
- **X** → `index.x3.val` (显示图形尺寸平均值)

### 数据格式转换
```c
// 距离数据转换 (保留2位小数，乘以100发送)
int distance_int = (int)(avg_distance * 100);
sprintf(hmi_cmd, "index.x2.val=%d\xff\xff\xff", distance_int);

// 图形尺寸转换
int shape_int = (int)(avg_shape * 100);
sprintf(hmi_cmd, "index.x3.val=%d\xff\xff\xff", shape_int);
```

## 🧪 测试步骤

### 1. 编译和烧录
1. 编译更新后的代码
2. 烧录到STM32
3. 观察启动信息

### 2. 数据采集测试
1. 运行摄像头程序 `69c.py`
2. 观察串口输出的采集信息：
   ```
   Distance sample: 12.34 (buffer: 5/20, avg: 12.15)
   Shape sample: 5.67 (buffer: 3/20, avg: 5.45)
   ```

### 3. 按键功能测试
1. 在HMI界面按下"开始"按键
2. 观察串口输出：
   ```
   === HMI Command Received ===
   HMI data: @开始
   Start button pressed!
   Button pressed! Distance updated: 12.15 (avg of 5 samples)
   Button pressed! Shape updated: 5.45 (avg of 3 samples)
   === Display updated with average values ===
   ```
3. 确认HMI显示屏更新了数值

### 4. 保持功能测试
1. 按键后继续发送摄像头数据
2. 确认HMI显示不变
3. 再次按键确认显示更新

## 🔍 调试信息

### 数据采集阶段
```
Distance sample: 12.34 (buffer: 5/20, avg: 12.15)
Shape sample: 5.67 (buffer: 3/20, avg: 5.45)
```

### 按键触发阶段
```
=== HMI Command Received ===
HMI data: @开始
Start button pressed!
Button pressed! Distance updated: 12.15 (avg of 5 samples)
=== Display updated with average values ===
```

## ⚙️ 配置参数

### 缓冲区大小
```c
#define AVERAGE_BUFFER_SIZE 20  // 可调整缓冲区大小
```

### 数据范围限制
```c
if(distance_int > 9999) distance_int = 9999;  // 最大值限制
if(distance_int < 0) distance_int = 0;        // 最小值限制
```

## 🎯 优势特点

1. **数据稳定**：通过平均值减少测量噪声
2. **用户控制**：按键触发，避免频繁更新
3. **实时采集**：后台持续收集数据
4. **状态保持**：显示稳定，便于读取
5. **调试友好**：详细的串口输出信息

## 🔧 故障排除

### 如果按键无响应
1. 检查HMI按键设置是否正确
2. 确认发送格式为`@开始\n`
3. 查看串口是否收到HMI命令

### 如果显示不更新
1. 检查控件名称是否为x2和x3
2. 确认数据格式转换是否正确
3. 验证HMI通信是否正常

### 如果平均值异常
1. 检查缓冲区是否有数据
2. 确认摄像头数据格式正确
3. 查看数据解析是否成功
