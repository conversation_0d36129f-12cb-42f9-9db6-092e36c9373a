#!/usr/bin/env python3
"""
HMI控件测试脚本
用于测试STM32串口屏的控件名称和位置

使用方法：
1. 连接STM32到电脑
2. 修改下面的串口号
3. 运行脚本
4. 观察串口屏上哪个位置显示了哪个数字
"""

import serial
import time

# 配置串口 - 请根据实际情况修改
SERIAL_PORT = "COM3"  # Windows: COM3, Linux: /dev/ttyUSB0, Mac: /dev/tty.usbserial-xxx
BAUD_RATE = 115200

def test_hmi_controls():
    """测试HMI控件名称"""
    try:
        # 打开串口
        ser = serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1)
        print(f"已连接到 {SERIAL_PORT}")
        
        # 测试数据：控件名称 -> 显示值
        test_data = {
            "index.n0.val": 1111,
            "index.n1.val": 2222, 
            "index.n2.val": 3333,
            "index.n3.val": 4444,
            "index.n4.val": 5555,
            "index.n5.val": 6666,
            "index.n6.val": 7777,
            "index.n7.val": 8888,
        }
        
        print("\n开始测试HMI控件...")
        print("请观察串口屏上的显示，记录哪个数字出现在哪个位置：")
        print("- 1111 对应 n0")
        print("- 2222 对应 n1") 
        print("- 3333 对应 n2")
        print("- 4444 对应 n3")
        print("- 5555 对应 n4")
        print("- 6666 对应 n5")
        print("- 7777 对应 n6")
        print("- 8888 对应 n7")
        print()
        
        # 发送测试数据
        for control_name, value in test_data.items():
            command = f"{control_name}={value}\xff\xff\xff"
            ser.write(command.encode())
            print(f"发送: {control_name}={value}")
            time.sleep(0.5)  # 等待显示更新
            
        print("\n测试完成！")
        print("请根据串口屏上的显示确定：")
        print("- 哪个控件对应'距离D'位置")
        print("- 哪个控件对应'X'位置")
        print("\n然后修改main.c中UpdateHMIDisplay()函数的控件名称")
        
        # 清零显示
        print("\n5秒后清零所有显示...")
        time.sleep(5)
        for control_name in test_data.keys():
            command = f"{control_name}=0\xff\xff\xff"
            ser.write(command.encode())
            time.sleep(0.1)
            
        ser.close()
        print("串口已关闭")
        
    except serial.SerialException as e:
        print(f"串口错误: {e}")
        print("请检查：")
        print("1. 串口号是否正确")
        print("2. STM32是否已连接")
        print("3. 串口是否被其他程序占用")
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"未知错误: {e}")

def test_distance_and_shape():
    """测试距离和图形数据发送"""
    try:
        ser = serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1)
        print(f"已连接到 {SERIAL_PORT}")
        
        test_commands = [
            "@DISTANCE:12.34",
            "@CIRCLE:5.67", 
            "@TRIANGLE:8.90",
            "@SQUARE:3.45",
            "@NO_DETECTION:0.00"
        ]
        
        print("\n测试摄像头数据格式...")
        for cmd in test_commands:
            full_cmd = cmd + "\n"
            ser.write(full_cmd.encode())
            print(f"发送: {cmd}")
            time.sleep(2)  # 等待处理和显示
            
        ser.close()
        print("测试完成")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    print("HMI控件测试脚本")
    print("================")
    print("1. 测试控件名称")
    print("2. 测试数据格式")
    
    choice = input("请选择测试类型 (1/2): ").strip()
    
    if choice == "1":
        test_hmi_controls()
    elif choice == "2":
        test_distance_and_shape()
    else:
        print("无效选择")
