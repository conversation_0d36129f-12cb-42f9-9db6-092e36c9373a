# HMI控件名称确认 ✅

## 问题已解决！
根据您提供的HMI界面截图，我已经确认了正确的控件名称：

## ✅ 正确的控件名称
- **距离D** → `index.n2.val` (已确认)
- **X** → `index.n4.val` (已确认)

代码已经修正为使用正确的控件名称。

## 如何确定正确的控件名称

### 方法1：查看HMI工程文件
1. 打开您的HMI工程文件
2. 查看每个数字显示控件的属性
3. 记录控件的`objname`或`vscope`属性

### 方法2：通过串口测试
您可以通过串口发送以下测试命令来确定正确的控件名称：

```
index.n0.val=1111\xff\xff\xff
index.n1.val=2222\xff\xff\xff
index.n2.val=3333\xff\xff\xff
index.n3.val=4444\xff\xff\xff
index.n4.val=5555\xff\xff\xff
index.n5.val=6666\xff\xff\xff
index.n6.val=7777\xff\xff\xff
index.n7.val=8888\xff\xff\xff
```

观察哪个数字出现在哪个位置，从而确定：
- 哪个控件对应"距离D"
- 哪个控件对应"X"

### 方法3：根据界面布局推测
根据您的界面截图，从上到下、从左到右的布局：
- 电流 (可能是 n0 或 n1)
- 功耗 (可能是 n1 或 n2)  
- 距离D (可能是 n2 或 n3)
- X (可能是 n4 或 n5)
- 最小X (可能是 n5 或 n6)

## 如果控件名称不正确

如果我猜测的控件名称不正确，请按以下步骤修改：

1. 确定正确的控件名称（如 n1, n2, n4 等）
2. 修改 `UpdateHMIDisplay()` 函数中的控件名称
3. 重新编译和测试

例如，如果"距离D"对应 `index.n1.val`，"X"对应 `index.n4.val`，则修改：
```c
sprintf(hmi_cmd, "index.n1.val=%d\xff\xff\xff", distance_int);  // 距离D
sprintf(hmi_cmd, "index.n4.val=%d\xff\xff\xff", shape_size_int); // X
```

## 测试步骤

1. 编译并烧录修改后的代码
2. 运行摄像头程序
3. 观察数据是否显示在正确位置
4. 如果位置不对，使用上述方法确定正确的控件名称
5. 修改代码并重新测试

## 预期结果

修改后，您应该看到：
- 摄像头测量的距离显示在"距离D"右边的方块中
- 图形的直径或边长显示在"X"右边的方块中
- 不再有数据显示在"电流"位置（除非您有其他电流测量功能）
