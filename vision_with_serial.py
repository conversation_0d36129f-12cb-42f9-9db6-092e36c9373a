from maix import image, display, app, time, camera, uart
import cv2
import numpy as np
import math

# --- 串口通信和数据存储 ---

# 串口初始化 - 与单片机通信
serial = uart.UART("/dev/ttyS0", 115200)

# 全局变量存储测量数据
measured_distance = 0.0
measured_diameter = 0.0
measured_side_length = 0.0
shape_type = "none"  # "circle", "triangle", "square", "none"

# --- Global Constants and Initialization ---

# Maix display and camera initialization
cam_width = 640
cam_height = 480
cam = camera.Camera(cam_width, cam_height, image.Format.FMT_BGR888)
disp = display.Display()

# Known A4 paper short edge actual width, in centimeters (for distance calculation)
KNOWN_WIDTH = 21  # cm

# Camera focal length, in pixels (This value MUST be recalibrated for 640x480 resolution)
FOCAL_LENGTH = 1216 # Updated to match wec.py calibration

# Rectangle filtering parameters (for A4 paper detection)
min_area = 5005 * (cam_width * cam_height) // (640 * 480)  # Updated to match wec.py
max_area = 120000 * (cam_width * cam_height) // (640 * 480) # Updated to match wec.py
min_aspect_ratio = 0.3        # Updated to match wec.py
max_aspect_ratio = 3.0        # Updated to match wec.py
corner_threshold = 8          # Threshold for corner proximity, used for merging rectangles

# --- Key parameters for black and white graphic detection ---
THRESHOLD_DARK_OBJECT = 60      # Pixels with grayscale value below this threshold are considered part of a black object
THRESHOLD_WHITE_OBJECT = 200    # Pixels with grayscale value above this threshold are considered part of a white object

# Graphic area thresholds
MIN_GRAPHIC_AREA = 50 * (cam_width * cam_height) // (640 * 480)  # Updated to match wec.py
MAX_GRAPHIC_AREA = int(0.9 * cam_width * cam_height) # Approx. 0.9 * 640 * 480 = 276480

# Triangle detection approximation precision parameter
TRIANGLE_APPROX_EPSILON_FACTOR = 0.008 # Further reduced from 0.015 to 0.008

# Tolerance for equilateral triangle side equality (percentage).
EQUILATERAL_TOLERANCE_PERCENT = 0.05 # 5% tolerance for side equality

# Square detection side equality tolerance (percentage)
SQUARE_SIDE_EQUALITY_TOLERANCE = 0.08 # Retain 8%

# --- 串口通信函数 ---

def send_measurement_data():
    """发送测量数据到单片机"""
    global measured_distance, measured_diameter, measured_side_length, shape_type
    
    try:
        if shape_type == "circle" and measured_diameter > 0:
            # 发送圆形直径
            data_to_send = f"CIRCLE:{measured_diameter:.2f}"
            print(f"[SERIAL] 发送圆形数据: 直径 = {measured_diameter:.2f} cm")
        elif shape_type == "triangle" and measured_side_length > 0:
            # 发送三角形边长
            data_to_send = f"TRIANGLE:{measured_side_length:.2f}"
            print(f"[SERIAL] 发送三角形数据: 边长 = {measured_side_length:.2f} cm")
        elif shape_type == "square" and measured_side_length > 0:
            # 发送正方形边长
            data_to_send = f"SQUARE:{measured_side_length:.2f}"
            print(f"[SERIAL] 发送正方形数据: 边长 = {measured_side_length:.2f} cm")
        elif measured_distance > 0:
            # 只发送距离
            data_to_send = f"DISTANCE:{measured_distance:.2f}"
            print(f"[SERIAL] 发送距离数据: 距离 = {measured_distance:.2f} cm")
        else:
            # 没有检测到有效数据
            data_to_send = "NO_DETECTION:0.00"
            print("[SERIAL] 发送: 无检测数据")
        
        # 发送数据到串口
        string_to_send = f'@{data_to_send}\n'
        data_bytes = string_to_send.encode('utf-8')
        serial.write(data_bytes)
        
    except Exception as e:
        print(f"[ERROR] 串口发送失败: {e}")

def print_measurement_summary():
    """在终端打印测量结果摘要"""
    global measured_distance, measured_diameter, measured_side_length, shape_type
    
    print("\n" + "="*50)
    print("📏 测量结果摘要")
    print("="*50)
    print(f"距离: {measured_distance:.2f} cm")
    
    if shape_type == "circle":
        print(f"检测到圆形 - 直径: {measured_diameter:.2f} cm")
    elif shape_type == "triangle":
        print(f"检测到三角形 - 边长: {measured_side_length:.2f} cm")
    elif shape_type == "square":
        print(f"检测到正方形 - 边长: {measured_side_length:.2f} cm")
    else:
        print("未检测到图形")
    
    print("="*50 + "\n")

# --- Helper Functions ---

def distance_to_camera(known_width, focal_length, pixel_width):
    """
    Calculates the distance from the object to the camera.
    Formula: Distance = (Known actual width * Focal length) / Object pixel width in image
    """
    if pixel_width == 0:
        return float('inf') # Avoid division by zero
    return (known_width * focal_length) / pixel_width

def find_circles_in_area(full_frame_to_draw_on, x_roi_offset, y_roi_offset, roi_width, roi_height, paper_pixel_width_for_scale):
    """
    Detects circles within the specified area and draws them along with their estimated real-world diameters.
    Returns the detected maximum circle diameter (cm)
    """
    global measured_diameter, shape_type
    max_diameter = 0.0
    
    if roi_width <= 0 or roi_height <= 0:
        return full_frame_to_draw_on

    A4_roi = full_frame_to_draw_on[y_roi_offset : y_roi_offset + roi_height,
                                   x_roi_offset : x_roi_offset + roi_width].copy()

    if A4_roi.shape[0] == 0 or A4_roi.shape[1] == 0:
        return full_frame_to_draw_on

    gray_roi = cv2.cvtColor(A4_roi, cv2.COLOR_BGR2GRAY)
    blurred_roi = cv2.GaussianBlur(gray_roi, (11, 11), 3)

    # Hough circle parameters need to be adjusted based on resolution
    hough_min_dist = 30 * (cam_width / 320) # 60
    hough_min_radius = 10 * (cam_width / 320) # 20
    hough_max_radius = 80 * (cam_width / 320) # 160

    circles = cv2.HoughCircles(blurred_roi, cv2.HOUGH_GRADIENT, dp=1.2, minDist=int(hough_min_dist), 
                               param1=50, param2=60, minRadius=int(hough_min_radius), maxRadius=int(hough_max_radius)) 
    
    if circles is not None:
        circles = np.round(circles[0, :]).astype("int")
        
        if paper_pixel_width_for_scale == 0:
            return full_frame_to_draw_on

        cm_per_pixel = KNOWN_WIDTH / paper_pixel_width_for_scale
        
        for (cx_roi, cy_roi, r) in circles:
            try:
                mask = np.zeros_like(gray_roi)
                cv2.circle(mask, (cx_roi, cy_roi), r, 255, -1) 
                
                circle_contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                if circle_contours:
                    main_circle_contour = circle_contours[0]
                    area_contour = cv2.contourArea(main_circle_contour)
                    perimeter_contour = cv2.arcLength(main_circle_contour, True)
                    
                    if perimeter_contour > 0:
                        circularity = 4 * np.pi * area_contour / (perimeter_contour**2)
                        
                        if 0.85 < circularity < 1.15: 
                            cx_full = cx_roi + x_roi_offset
                            cy_full = cy_roi + y_roi_offset

                            diameter_cm = 2 * r * cm_per_pixel
                            
                            # 记录最大的圆形直径
                            if diameter_cm > max_diameter:
                                max_diameter = diameter_cm
                                measured_diameter = diameter_cm
                                shape_type = "circle"
                                print(f"[DETECT] 圆形检测: 直径 = {diameter_cm:.2f} cm")
                            
                            cv2.circle(full_frame_to_draw_on, (cx_full, cy_full), r, (0, 255, 0), 2) 
                            cv2.putText(full_frame_to_draw_on, f"{diameter_cm:.1f}cm", (cx_full - int(30 * (cam_width / 320)), cy_full + int(10 * (cam_width / 320))), 
                                        cv2.FONT_HERSHEY_SIMPLEX, 0.5 * (cam_width / 320), (0, 0, 255), 1) 
            except Exception as e:
                pass 

    return full_frame_to_draw_on
