# 摄像头数据显示到串口屏功能 - 完善版测试说明

## 功能概述

本功能实现了摄像头发送的测量数据在STM32串口屏上的实时显示。支持以下数据类型：

- **DISTANCE** (距离) → 显示到 `index.x2.val` (距离D位置)
- **TRIANGLE** (三角形边长) → 显示到 `index.x3.val` (X位置)
- **CIRCLE** (圆形直径) → 显示到 `index.x3.val` (X位置)
- **SQUARE** (正方形边长) → 显示到 `index.x3.val` (X位置)
- **NO_DETECTION** (无检测) → 清零显示

## 🔧 最新修改 (解决显示位置问题)

### 问题解决
- 修复了距离数据显示在"电流"位置的问题
- 现在距离正确显示在"距离D"右边的方块中
- 图形尺寸(直径/边长)显示在"X"右边的方块中

## 🔧 完善的功能特性

### 1. 可靠的数据传输
- 专用的 `UpdateHMIDisplay()` 函数
- 数值范围限制 (0-9999)
- 自动刷新命令发送
- 定期更新机制 (每5000次循环)

### 2. 改进的HMI通信
- 增加延时确保数据传输稳定
- 发送刷新命令 (`ref index.n2`, `ref index.n4`)
- 初始化时测试HMI连接
- 错误处理和调试输出

## 数据格式

### 摄像头发送格式 (69c.py)
```
@DISTANCE:12.34
@TRIANGLE:5.67
@CIRCLE:8.90
@SQUARE:4.56
@NO_DETECTION:0.00
@123.45  (兼容旧格式)
```

### STM32处理逻辑 (main.c)
1. 接收串口数据并解析类型标识
2. 根据类型发送到对应的串口屏控件：
   - 距离 → index.x2.val (距离D位置)
   - 图形尺寸 → index.x3.val (X位置，优先级：圆形>三角形>正方形)
3. 数值乘以100转换为整数发送 (例: 12.34 → 1234)

## 串口屏显示命令 (修正版 - 正确位置显示)
```c
// 专用HMI更新函数 - 修正显示位置
void UpdateHMIDisplay(void)
{
    // 距离显示到"距离D"位置 (index.n2.val)
    int distance_int = (int)(distance_value * 100);
    if(distance_int > 9999) distance_int = 9999;
    if(distance_int < 0) distance_int = 0;
    sprintf(hmi_cmd, "index.n2.val=%d\xff\xff\xff", distance_int);
    Serial2_SendString(hmi_cmd);
    Delay_ms(30);

    // 图形尺寸显示到"X"位置 (index.n4.val)
    // 优先级：圆形 > 三角形 > 正方形
    float shape_size = 0.0f;
    if(circle_value > 0) {
        shape_size = circle_value;  // 圆形直径
    } else if(triangle_value > 0) {
        shape_size = triangle_value;  // 三角形最大边长
    } else if(square_value > 0) {
        shape_size = square_value;  // 正方形边长
    }

    int shape_size_int = (int)(shape_size * 100);
    if(shape_size_int > 9999) shape_size_int = 9999;
    if(shape_size_int < 0) shape_size_int = 0;
    sprintf(hmi_cmd, "index.n4.val=%d\xff\xff\xff", shape_size_int);
    Serial2_SendString(hmi_cmd);
    Delay_ms(30);

    // 发送刷新命令
    Serial2_SendString("ref index.n2\xff\xff\xff");
    Delay_ms(10);
    Serial2_SendString("ref index.n4\xff\xff\xff");
    Delay_ms(10);
}
```

## 测试步骤

### 1. 编译和烧录
1. 打开Keil工程
2. 编译项目
3. 烧录到STM32

### 2. 串口监控
1. 连接串口调试工具 (115200波特率)
2. 观察启动信息和心跳信息

### 3. 摄像头测试
1. 运行摄像头程序 (69c.py)
2. 在摄像头前放置A4纸和几何图形
3. 观察串口输出和串口屏显示

### 4. 手动测试
通过串口发送以下测试数据：
```
@DISTANCE:12.34
@TRIANGLE:5.67
@CIRCLE:8.90
@NO_DETECTION:0.00
```

## 预期结果

### 串口调试输出
```
STM32 Started!
Waiting for Camera Data on USART1...
Supported formats:
  @DISTANCE:xx.xx -> index.n2.val
  @TRIANGLE:xx.xx -> index.n4.val
  ...

=== RX from Camera ===
Raw data: DISTANCE:12.34
Parsed DISTANCE: 12.34
Sending DISTANCE to HMI: index.n2.val=1234
```

### 串口屏显示
- `index.n2.val` 显示距离值 (乘以100) - "距离D"位置
- `index.n4.val` 显示图形尺寸 (乘以100) - "X"位置

## 故障排除

### 1. 数据接收问题
- 检查串口连接和波特率 (115200)
- 确认摄像头正常发送数据
- 观察心跳信息中的LineFlag状态

### 2. 解析问题
- 检查数据格式是否正确
- 观察调试输出中的原始数据
- 确认字符串结束符处理

### 3. 串口屏显示问题
- 检查串口2连接 (PA2-TX, PA3-RX, 115200波特率)
- 确认HMI控件名称 (index.n2.val, index.n4.val)
- 检查数据格式和结束符 (\xff\xff\xff)
- 观察初始化时的HMI测试输出
- 检查数值范围是否在0-9999之间
- 确认HMI屏幕是否支持ref刷新命令

### 4. 新增调试功能
- 定期更新机制 (每5000次循环自动刷新)
- 初始化时HMI连接测试
- 数值范围自动限制
- 详细的调试输出信息

## 代码修改说明

### 主要修改文件
- `User/main.c` - 添加数据解析和显示逻辑
- 新增全局变量存储不同类型的测量数据

### 关键函数
- `strncmp()` - 字符串比较识别数据类型
- `sscanf()` - 解析数值
- `strcpy()` - 复制字符串
- `sprintf()` - 格式化输出

### 兼容性
- 保持对原有 `@123.45` 格式的兼容
- 不影响现有的串口通信功能
