from maix import image, display, app, time, camera, uart
import cv2
import numpy as np
import math


# --- 全局常量和初始化 ---
serial = uart.UART("/dev/ttyS0", 115200)
# Maix 显示器和摄像头初始化
cam_width = 640
cam_height = 480
cam = camera.Camera(cam_width, cam_height, image.Format.FMT_BGR888) 
disp = display.Display()


# 已知 A4 纸的短边实际宽度，单位厘米 (用于距离计算)
KNOWN_WIDTH = 21  # cm 

# 摄像头焦距，单位像素 (此值必须为 640x480 分辨率重新校准)
# !!! 重要提示：此 FOCAL_LENGTH 值必须重新校准 !!!
# 校准方法：FOCAL_LENGTH = (已知物体像素宽度 * 物体实际距离) / 物体实际宽度
# 例如，在 60cm 处放置 21cm 宽的 A4 纸，测得其像素宽度为 X，则 FOCAL_LENGTH = (X * 60) / 21
FOCAL_LENGTH = 1216 # <--- !!! 请根据 640x480 分辨率进行实际校准和更新此值 !!!
                     # 估算值：假设焦距与分辨率成正比，1429.2 * (640/320) = 2858.4
                     # 实际使用中请务必校准！

# 矩形过滤参数 (用于检测A4纸)
min_area = 5005 * (cam_width * cam_height) // (640 * 480)  # 5000 * 4 = 20000
max_area = 120000 * (cam_width * cam_height) // (640 * 480) # 65000 * 4 = 260000
min_aspect_ratio = 0.3        # 最小长宽比 (宽度/高度)
max_aspect_ratio = 3.0        # 最大长宽比 (宽度/高度)
corner_threshold = 8          # 角点位置接近的阈值，用于合并矩形

# --- 黑白图形检测的关键参数 ---
THRESHOLD_DARK_OBJECT = 60      # 灰度值低于此阈值的像素被视为黑色物体的一部分 (例如，0-60)
THRESHOLD_WHITE_OBJECT = 200    # 灰度值高于此阈值的像素被视为白色物体的一部分 (例如，200-255)

# 图形面积阈值也按分辨率比例放大，并将 MAX_GRAPHIC_AREA 大幅放宽，以适应近距离大面积图形
MIN_GRAPHIC_AREA = 50 * (cam_width * cam_height) // (640 * 480)  # 50 * 4 = 200
# 增加 MAX_GRAPHIC_AREA，假设图形最大可以占据 ROI 的 90%
MAX_GRAPHIC_AREA = int(0.9 * cam_width * cam_height) # 约 0.9 * 640 * 480 = 276480，远大于之前的 260000

# 三角形检测的近似精度参数：微调，使其对大轮廓也能保持足够细节
# **为了减少三角形边长误差，将此值进一步减小，强制更精确的轮廓近似。**
TRIANGLE_APPROX_EPSILON_FACTOR = 0.008 # 从 0.015 进一步减小到 0.008

# 正方形检测的边长相等容忍度 (百分比)：调严格一些，减少误判
SQUARE_SIDE_EQUALITY_TOLERANCE = 0.08 # 保持 8%


# --- 以下是原 cv_lite 代码中引入但在此 cv2 代码中未使用的参数，已注释或移除 ---
# PURPLE_THRESHOLD = (20, 60, 15, 70, -70, -20) # 未使用
# BASE_RADIUS = 45              # 未使用
# POINTS_PER_CIRCLE = 24        # 未使用
# RECT_WIDTH = 210              # 未使用
# RECT_HEIGHT = 95              # 未使用
# canny_thresh1 = 50            # cv2 Canny的阈值在主循环中定义
# canny_thresh2 = 150           # cv2 Canny的阈值在主循环中定义
# approx_epsilon = 0.04         # cv2.approxPolyDP的 epsilon 在主循环中按比例计算
# area_min_ratio = 0.005        # 矩形面积过滤使用 min_area/max_area
# max_angle_cos = 0.3           # 矩形角度检查未在此处实现
# gaussian_blur_size = 3        # cv2.GaussianBlur使用 (5,5) 或 (3,3)

# --- 串口通信函数 ---
def send_distance_data(distance_cm):
    """发送距离数据到单片机"""
    try:
        data_to_send = f"@DISTANCE:{distance_cm:.2f}\n"
        data_bytes = data_to_send.encode('utf-8')
        serial.write(data_bytes)
        print(f"[SERIAL] 发送距离数据: {data_to_send.strip()}")
    except Exception as e:
        print(f"[ERROR] 距离数据发送失败: {e}")

def send_circle_data(diameter_cm):
    """发送圆形直径数据到单片机"""
    try:
        data_to_send = f"@CIRCLE:{diameter_cm:.2f}\n"
        data_bytes = data_to_send.encode('utf-8')
        serial.write(data_bytes)
        print(f"[SERIAL] 发送圆形数据: 直径 = {diameter_cm:.2f} cm")
    except Exception as e:
        print(f"[ERROR] 圆形数据发送失败: {e}")

def send_triangle_data(side_length_cm):
    """发送三角形边长数据到单片机"""
    try:
        data_to_send = f"@TRIANGLE:{side_length_cm:.2f}\n"
        data_bytes = data_to_send.encode('utf-8')
        serial.write(data_bytes)
        print(f"[SERIAL] 发送三角形数据: 边长 = {side_length_cm:.2f} cm")
    except Exception as e:
        print(f"[ERROR] 三角形数据发送失败: {e}")

def send_square_data(side_length_cm):
    """发送正方形边长数据到单片机"""
    try:
        data_to_send = f"@SQUARE:{side_length_cm:.2f}\n"
        data_bytes = data_to_send.encode('utf-8')
        serial.write(data_bytes)
        print(f"[SERIAL] 发送正方形数据: 边长 = {side_length_cm:.2f} cm")
    except Exception as e:
        print(f"[ERROR] 正方形数据发送失败: {e}")

def send_no_detection():
    """发送无检测数据到单片机"""
    try:
        data_to_send = "@NO_DETECTION:0.00\n"
        data_bytes = data_to_send.encode('utf-8')
        serial.write(data_bytes)
        print("[SERIAL] 发送: 无检测数据")
    except Exception as e:
        print(f"[ERROR] 无检测数据发送失败: {e}")

# --- 兼容性函数（保持原有调用方式） ---
def chuankoufasong1(tdate):
    """兼容性函数：发送距离数据"""
    send_distance_data(tdate)

def chuankoufasong2(tdate):
    """兼容性函数：发送图形数据（默认为圆形）"""
    send_circle_data(tdate)

def distance_to_camera(known_width, focal_length, pixel_width):
    """
    计算物体到摄像头的距离。
    公式：距离 = (已知实际宽度 * 焦距) / 物体在图像中的像素宽度
    """
    if pixel_width == 0:
        return float('inf') # 避免除以零
    return (known_width * focal_length) / pixel_width

def find_circles_in_area(full_frame_to_draw_on, x_roi_offset, y_roi_offset, roi_width, roi_height, paper_pixel_width_for_scale):
    """
    在指定区域内检测圆形，并绘制它们及其估计的真实世界直径。
    """
    if roi_width <= 0 or roi_height <= 0:
        return full_frame_to_draw_on

    A4_roi = full_frame_to_draw_on[y_roi_offset : y_roi_offset + roi_height,
                                   x_roi_offset : x_roi_offset + roi_width].copy()

    if A4_roi.shape[0] == 0 or A4_roi.shape[1] == 0:
        return full_frame_to_draw_on

    gray_roi = cv2.cvtColor(A4_roi, cv2.COLOR_BGR2GRAY)
    blurred_roi = cv2.GaussianBlur(gray_roi, (11, 11), 3) # 模糊核大小可能需要调整，这里不变

    # 霍夫圆参数需要根据分辨率进行调整，特别是minDist和半径范围
    hough_min_dist = 30 * (cam_width / 640) # 60
    hough_min_radius = 10 * (cam_width / 640) # 20
    hough_max_radius = 80 * (cam_width / 640) # 160

    circles = cv2.HoughCircles(blurred_roi, cv2.HOUGH_GRADIENT, dp=1.2, minDist=int(hough_min_dist), 
                               param1=50, param2=60, minRadius=int(hough_min_radius), maxRadius=int(hough_max_radius)) 
    
    if circles is not None:
        circles = np.round(circles[0, :]).astype("int")
        
        if paper_pixel_width_for_scale == 0:
            return full_frame_to_draw_on

        cm_per_pixel = KNOWN_WIDTH / paper_pixel_width_for_scale
        
        for (cx_roi, cy_roi, r) in circles:
            try:
                mask = np.zeros_like(gray_roi)
                cv2.circle(mask, (cx_roi, cy_roi), r, 255, -1) 
                
                circle_contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                if circle_contours:
                    main_circle_contour = circle_contours[0]
                    area_contour = cv2.contourArea(main_circle_contour)
                    perimeter_contour = cv2.arcLength(main_circle_contour, True)
                    
                    if perimeter_contour > 0:
                        circularity = 4 * np.pi * area_contour / (perimeter_contour**2)
                        
                        if 0.85 < circularity < 1.15: 
                            cx_full = cx_roi + x_roi_offset
                            cy_full = cy_roi + y_roi_offset

                            diameter_cm = 2 * r * cm_per_pixel
                            # 发送圆形直径数据
                            data_to_send = f"@CIRCLE:{diameter_cm:.2f}\n"
                            data_bytes = data_to_send.encode('utf-8')
                            serial.write(data_bytes)
                            print(f"[SERIAL] 发送圆形数据: 直径 = {diameter_cm:.2f} cm")
                            cv2.circle(full_frame_to_draw_on, (cx_full, cy_full), r, (0, 255, 0), 2) 
                            cv2.putText(full_frame_to_draw_on, f"{diameter_cm:.1f}cm", (cx_full - int(30 * (cam_width / 320)), cy_full + int(10 * (cam_width / 320))), 
                                        cv2.FONT_HERSHEY_SIMPLEX, 0.5 * (cam_width / 320), (0, 0, 255), 1) 
            except Exception as e:
                # print(f"圆形圆度检查失败: {e}") 
                pass 

    return full_frame_to_draw_on

def find_black_white_graphics_in_area(full_frame_to_draw_on, x_roi_offset, y_roi_offset, roi_width, roi_height, paper_pixel_width_for_scale):
    """
    在指定区域内检测黑色和白色图形的轮廓，并用不同颜色绘制。
    现在也支持识别三角形和正方形并显示其边长。
    """
    if roi_width <= 0 or roi_height <= 0:
        return full_frame_to_draw_on

    graphic_roi = full_frame_to_draw_on[y_roi_offset : y_roi_offset + roi_height,
                                         x_roi_offset : x_roi_offset + roi_width].copy()

    if graphic_roi.shape[0] == 0 or graphic_roi.shape[1] == 0:
        return full_frame_to_draw_on

    gray_graphic_roi = cv2.cvtColor(graphic_roi, cv2.COLOR_BGR2GRAY)
    
    # 将模糊核调小，以保留更多边缘细节
    blurred_gray_roi = cv2.GaussianBlur(gray_graphic_roi, (3, 3), 0) # 从 (5,5) 改为 (3,3)
    
    cm_per_pixel = 0.0
    if paper_pixel_width_for_scale > 0:
        cm_per_pixel = KNOWN_WIDTH / paper_pixel_width_for_scale

    def get_centroid(c):
        M = cv2.moments(c)
        if M["m00"] != 0:
            cX = int(M["m10"] / M["m00"])
            cY = int(M["m01"] / M["m00"])
        else: 
            x_b, y_b, w_b, h_b = cv2.boundingRect(c)
            cX = x_b + w_b // 2
            cY = y_b + h_b // 2
        return cX, cY

    # 1. 检测黑色图形 (暗像素在亮背景上)
    _, black_mask = cv2.threshold(blurred_gray_roi, THRESHOLD_DARK_OBJECT, 255, cv2.THRESH_BINARY_INV)
    black_contours, _ = cv2.findContours(black_mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)

    for c in black_contours:
        area = cv2.contourArea(c)
        if MIN_GRAPHIC_AREA < area < MAX_GRAPHIC_AREA: 
            perimeter = cv2.arcLength(c, True)
            # 使用更小的 epsilon，以获得更精确的近似
            approx = cv2.approxPolyDP(c, TRIANGLE_APPROX_EPSILON_FACTOR * perimeter, True) 

            shape_detected = False 

            if len(approx) == 3: # 识别为三角形
                c_full = c + [x_roi_offset, y_roi_offset] 
                p1 = approx[0][0]
                p2 = approx[1][0]
                p3 = approx[2][0]

                side1_pixels = np.linalg.norm(p1 - p2)
                side2_pixels = np.linalg.norm(p2 - p3)
                side3_pixels = np.linalg.norm(p3 - p1)

                side1_cm = side1_pixels * cm_per_pixel
                side2_cm = side2_pixels * cm_per_pixel
                side3_cm = side3_pixels * cm_per_pixel
                sidepingjun_cm = (side1_cm +side2_cm +side3_cm)/3
                max_side_cm = max(side1_cm, side2_cm, side3_cm)
                # 发送三角形边长数据
                data_to_send = f"@TRIANGLE:{max_side_cm:.2f}\n"
                data_bytes = data_to_send.encode('utf-8')
                serial.write(data_bytes)
                print(f"[SERIAL] 发送三角形数据: 边长 = {max_side_cm:.2f} cm")
                cv2.drawContours(full_frame_to_draw_on, [c_full], -1, (255, 0, 0), 2)
                
                cX, cY = get_centroid(c)
                cX_full = cX + x_roi_offset
                cY_full = cY + y_roi_offset

                text_to_display = f"Tri: {side1_cm:.1f},{side2_cm:.1f},{side3_cm:.1f}cm"
                cv2.putText(full_frame_to_draw_on, text_to_display, (cX_full - int(50 * (cam_width / 320)), cY_full - int(10 * (cam_height / 240))),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.4 * (cam_width / 320), (255, 0, 255), 1) 
                shape_detected = True

            elif len(approx) == 4: # 识别为四边形，进一步判断是否为正方形
                # 获取最小外接旋转矩形
                rect = cv2.minAreaRect(c)
                (center_x, center_y), (width, height), angle = rect

                # 确保 width 总是短边，height 总是长边
                if width > height:
                    width, height = height, width
                    angle += 90 # 调整角度

                # 检查边长是否近似相等
                is_square_sides = False
                if height > 0 and (height - width) / height < SQUARE_SIDE_EQUALITY_TOLERANCE:
                    is_square_sides = True

                # 检查角度是否近似为90度 (更严格的正方形判断)
                is_square_angles = True
                pts = approx.reshape(-1, 2) # 重新获取 approx 的点以便进行角度检查
                if is_square_sides: 
                    for i in range(4):
                        p_curr = pts[i]
                        p_prev = pts[(i - 1 + 4) % 4]
                        p_next = pts[(i + 1) % 4]
                        
                        v1 = p_prev - p_curr
                        v2 = p_next - p_curr
                        
                        dot_product = np.dot(v1, v2)
                        mag_v1 = np.linalg.norm(v1)
                        mag_v2 = np.linalg.norm(v2)
                        
                        if mag_v1 == 0 or mag_v2 == 0:
                            is_square_angles = False 
                            break

                        cosine_angle = dot_product / (mag_v1 * mag_v2)
                        
                        if abs(cosine_angle) > 0.2: # 允许一定的角度偏差
                            is_square_angles = False
                            break
                else: 
                    is_square_angles = False


                if is_square_sides and is_square_angles: # 必须边长和角度都符合才认为是正方形
                    c_full = c + [x_roi_offset, y_roi_offset] 
                    
                    # 使用 minAreaRect 得到的宽度来计算真实边长
                    square_side_pixels = (width + height) / 2 # 取平均值作为边长
                    square_side_cm = square_side_pixels * cm_per_pixel
                    # 发送正方形边长数据
                    data_to_send = f"@SQUARE:{square_side_cm:.2f}\n"
                    data_bytes = data_to_send.encode('utf-8')
                    serial.write(data_bytes)
                    print(f"[SERIAL] 发送正方形数据: 边长 = {square_side_cm:.2f} cm")
                    cv2.drawContours(full_frame_to_draw_on, [c_full], -1, (0, 255, 255), 2) # Yellow
                    
                    cX, cY = get_centroid(c)
                    cX_full = cX + x_roi_offset
                    cY_full = cY + y_roi_offset

                    text_to_display = f"Sq: {square_side_cm:.1f}cm"
                    cv2.putText(full_frame_to_draw_on, text_to_display, (cX_full - int(30 * (cam_width / 320)), cY_full - int(10 * (cam_height / 240))),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.4 * (cam_width / 320), (0, 165, 255), 1) # Orange
                    shape_detected = True

            if not shape_detected: 
                c_full = c + [x_roi_offset, y_roi_offset] 
                cv2.drawContours(full_frame_to_draw_on, [c_full], -1, (255, 0, 0), 2) # BGR (蓝色)

    # 2. 检测白色图形 (亮像素在暗背景上)
    _, white_mask = cv2.threshold(blurred_gray_roi, THRESHOLD_WHITE_OBJECT, 255, cv2.THRESH_BINARY)
    white_contours, _ = cv2.findContours(white_mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)

    for c in white_contours:
        area = cv2.contourArea(c)
        if MIN_GRAPHIC_AREA < area < MAX_GRAPHIC_AREA: 
            perimeter = cv2.arcLength(c, True)
            # 使用更小的 epsilon，以获得更精确的近似
            approx = cv2.approxPolyDP(c, TRIANGLE_APPROX_EPSILON_FACTOR * perimeter, True)

            shape_detected = False

            if len(approx) == 3: 
                c_full = c + [x_roi_offset, y_roi_offset]
                p1 = approx[0][0]
                p2 = approx[1][0]
                p3 = approx[2][0]

                side1_pixels = np.linalg.norm(p1 - p2)
                side2_pixels = np.linalg.norm(p2 - p3)
                side3_pixels = np.linalg.norm(p3 - p1)

                side1_cm = side1_pixels * cm_per_pixel
                side2_cm = side2_pixels * cm_per_pixel
                side3_cm = side3_pixels * cm_per_pixel

                cv2.drawContours(full_frame_to_draw_on, [c_full], -1, (255, 0, 255), 2) # Magenta
                
                cX, cY = get_centroid(c)
                cX_full = cX + x_roi_offset
                cY_full = cY + y_roi_offset

                text_to_display = f"Tri: {side1_cm:.1f},{side2_cm:.1f},{side3_cm:.1f}cm"
                cv2.putText(full_frame_to_draw_on, text_to_display, (cX_full - int(50 * (cam_width / 320)), cY_full - int(10 * (cam_height / 240))),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.4 * (cam_width / 320), (255, 255, 0), 1) # Cyan
                shape_detected = True

            elif len(approx) == 4: 
                # 获取最小外接旋转矩形
                rect = cv2.minAreaRect(c)
                (center_x, center_y), (width, height), angle = rect
                if width > height:
                    width, height = height, width
                    angle += 90 

                is_square_sides = False
                if height > 0 and (height - width) / height < SQUARE_SIDE_EQUALITY_TOLERANCE:
                    is_square_sides = True

                is_square_angles = True
                pts = approx.reshape(-1, 2)
                if is_square_sides:
                    for i in range(4):
                        p_curr = pts[i]
                        p_prev = pts[(i - 1 + 4) % 4]
                        p_next = pts[(i + 1) % 4]
                        
                        v1 = p_prev - p_curr
                        v2 = p_next - p_curr
                        
                        dot_product = np.dot(v1, v2)
                        mag_v1 = np.linalg.norm(v1)
                        mag_v2 = np.linalg.norm(v2)
                        
                        if mag_v1 == 0 or mag_v2 == 0:
                            is_square_angles = False
                            break

                        cosine_angle = dot_product / (mag_v1 * mag_v2)
                        if abs(cosine_angle) > 0.2: 
                            is_square_angles = False
                            break
                else:
                    is_square_angles = False

                if is_square_sides and is_square_angles: 
                    c_full = c + [x_roi_offset, y_roi_offset] 
                    square_side_pixels = (width + height) / 2 # 使用 minAreaRect 得到的宽度来计算真实边长
                    square_side_cm = square_side_pixels * cm_per_pixel

                    cv2.drawContours(full_frame_to_draw_on, [c_full], -1, (255, 255, 0), 2) # Light Blue
                    
                    cX, cY = get_centroid(c)
                    cX_full = cX + x_roi_offset
                    cY_full = cY + y_roi_offset

                    text_to_display = f"Sq: {square_side_cm:.1f}cm"
                    cv2.putText(full_frame_to_draw_on, text_to_display, (cX_full - int(30 * (cam_width / 320)), cY_full - int(10 * (cam_height / 240))),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.4 * (cam_width / 320), (0, 255, 255), 1) # Yellow
                    shape_detected = True

            if not shape_detected: 
                c_full = c + [x_roi_offset, y_roi_offset] 
                cv2.drawContours(full_frame_to_draw_on, [c_full], -1, (255, 0, 255), 2) # BGR (洋红色)

    return full_frame_to_draw_on


# --- 主应用程序循环 ---
print("MaixCam A4纸识别、距离测量与圆形/黑白图形检测系统启动...")
print(f"当前分辨率: {cam_width}x{cam_height}")
print(f"!!! 请务必重新校准 FOCAL_LENGTH ({FOCAL_LENGTH:.1f} 只是一个估算值) !!!")

while not app.need_exit():
    maix_img = cam.read()
    
    if maix_img is None:
        print("捕获图像失败，正在重试...")
        continue 
    
    img_cv = image.image2cv(maix_img, ensure_bgr=True, copy=False)
    display_frame = img_cv.copy() 
  
    # --- 矩形检测逻辑 (用于识别A4纸) ---
    gray = cv2.cvtColor(display_frame, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    # Canny 阈值可能需要根据新分辨率和光照重新调整。
    # 如果边缘太多或太少，调整 50 和 150。
    edged = cv2.Canny(blurred, 50, 150) 
    kernel = np.ones((3, 3), np.uint8)
    dilated_edges = cv2.dilate(edged, kernel, iterations=1) 
    contours, hierarchy = cv2.findContours(dilated_edges.copy(), cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    rectangles = []
    for contour in contours:
        perimeter = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True) 
        if len(approx) == 4: # 确保是四边形
            area = cv2.contourArea(approx)
            x, y, w, h = cv2.boundingRect(approx)
            aspect_ratio = float(w) / h if h != 0 else 0

            # 过滤面积和宽高比，以识别A4纸
            if min_area < area < max_area and min_aspect_ratio < aspect_ratio < max_aspect_ratio:
                rectangles.append(approx)

    merged_rectangles = []
    for rect in rectangles:
        found_match = False
        for i, merged_rect in enumerate(merged_rectangles):
            corner_matches = 0
            for corner1 in rect:
                for corner2 in merged_rect:
                    distance = np.linalg.norm(np.array(corner1[0]) - np.array(corner2[0]))
                    if distance < corner_threshold:
                        corner_matches += 1
                        break 
            if corner_matches >= 3: # 如果有至少3个角点接近，认为是同一个矩形
                found_match = True
                # 如果新矩形面积更大，则替换（此处可以根据实际需求调整，例如取平均或保留面积最小的）
                # 这里为了找到“最小”矩形，实际上我们不需要这种合并策略，但保留原代码结构
                if cv2.contourArea(rect) < cv2.contourArea(merged_rect): # 保留面积更小的
                    merged_rectangles[i] = rect
                break 
        if not found_match:
            merged_rectangles.append(rect)

    # ***关键修改：将矩形按面积从小到大排序***
    merged_rectangles.sort(key=lambda r: cv2.contourArea(r), reverse=False)

    # ***移除此行：原先用于只保留两个最大矩形的逻辑，现在不再需要***
    # if len(merged_rectangles) > 2:
    #     merged_rectangles = merged_rectangles[:2]

    # --- 整合距离计算和圆形/黑白图形检测 ---
    outer_frame_detected = False
    if len(merged_rectangles) >= 1:
        # ***修改：选择列表中第一个元素，即面积最小的矩形作为 A4 纸***
        outer_rect = merged_rectangles[0] 
        x_paper, y_paper, w_paper, h_paper = cv2.boundingRect(outer_rect)
        dist_cm = distance_to_camera(KNOWN_WIDTH, FOCAL_LENGTH, w_paper)
        # 发送距离数据
        data_to_send = f"@DISTANCE:{dist_cm:.2f}\n"
        data_bytes = data_to_send.encode('utf-8')
        serial.write(data_bytes)
        print(f"[SERIAL] 发送距离数据: 距离 = {dist_cm:.2f} cm")

        cv2.drawContours(display_frame, [outer_rect], -1, (0, 0, 255), 2) # 绘制检测到的A4纸
        
        cv2.putText(display_frame, f" {dist_cm:.1f} cm", (x_paper, y_paper - int(30 * (cam_height / 240))), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7 * (cam_width / 320), (0, 0, 255), 2) 
        
        outer_frame_detected = True

        # ***移除此段：原先用于绘制第二个矩形（内框）的逻辑，现在不再需要***
        # if len(merged_rectangles) == 2:
        #     inner_rect = merged_rectangles[1]
        #     x_inner, y_inner, w_inner, h_inner = cv2.boundingRect(inner_rect)
        #     cv2.drawContours(display_frame, [inner_rect], -1, (0, 255, 0), 2)
        #     cv2.putText(display_frame,"",  (x_inner, y_inner - int(10 * (cam_height / 240))),
        #                 cv2.FONT_HERSHEY_SIMPLEX, 0.5 * (cam_width / 320), (0, 255, 0), 2)

        # 在检测到的 A4 纸 (外框) 区域内检测并绘制圆形
        find_circles_in_area(display_frame, x_paper, y_paper, w_paper, h_paper, w_paper)
        
        # 在检测到的 A4 纸区域内检测并绘制黑白图形
        find_black_white_graphics_in_area(display_frame, x_paper, y_paper, w_paper, h_paper, w_paper)

    if not outer_frame_detected:
        cv2.putText(display_frame, " A4 no", (20, display_frame.shape[0] // 2),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7 * (cam_width / 320), (0, 0, 255), 2) 

    img_to_show = image.cv2image(display_frame, bgr=True, copy=False)
    
    disp.show(img_to_show)

print("MaixCam 系统已退出。")
